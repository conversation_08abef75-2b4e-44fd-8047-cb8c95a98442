%% RIS辅助FAS隐蔽通信系统优化
% 基于交替优化(AO)框架的联合优化算法
% 包含：波束成形优化、RIS相位优化、FAS位置优化

clear; clc; close all;

%% 系统参数设置
% 基本参数
M = 4;              % Alice天线数量
N = 16;             % RIS单元数量  
K = 2;              % 合法用户Bob数量
J = 2;              % 窃听者Eve数量
L = 2;              % 监听者Willie数量

% 物理参数
fc = 2.4e9;         % 载波频率 2.4GHz
c = 3e8;            % 光速
lambda = c/fc;      % 波长
d_min = lambda/2;   % 最小天线间距

% 功率和噪声参数
P_max = 1;          % Alice最大发射功率 (W)
sigma2_n = 1e-10;   % 噪声功率 (W)
epsilon_cov = 1e-8; % 隐蔽阈值

% 路径损耗参数
alpha_AR = 2.2;     % Alice-RIS路径损耗指数
alpha_RU = 2.8;     % RIS-用户路径损耗指数
beta_0 = -30;       % 参考距离路径损耗 (dB)

% 区域约束
region_size = 2;    % FAS天线部署区域大小 (m)
U_region = [-region_size/2, region_size/2; -region_size/2, region_size/2];

% 算法参数
max_iter_AO = 20;           % 交替优化最大迭代次数
tolerance = 1e-4;           % 收敛阈值
max_iter_BCD = 10;          % BCD内部迭代次数
GWO_pop_size = 30;          % GWO种群大小
GWO_max_iter = 50;          % GWO最大迭代次数

%% 初始化位置和信道
% 固定位置初始化
pos_RIS = [0, 5];                           % RIS位置
pos_Bob = [10*cos(2*pi*(0:K-1)/K)', 10*sin(2*pi*(0:K-1)/K)'];  % Bob位置
pos_Eve = [8*cos(2*pi*(0:J-1)/J + pi/4)', 8*sin(2*pi*(0:J-1)/J + pi/4)'];  % Eve位置  
pos_Willie = [6*cos(2*pi*(0:L-1)/L + pi/6)', 6*sin(2*pi*(0:L-1)/L + pi/6)'];  % Willie位置

% 初始化Alice天线位置
U = generate_initial_antenna_positions(M, U_region, d_min);

% 初始化RIS相位向量
v = exp(1j * 2*pi * rand(N, 1));

% 初始化波束成形向量
W = (randn(M, K) + 1j*randn(M, K)) / sqrt(2);
W = W / sqrt(K) * sqrt(P_max);  % 功率归一化

%% 主要交替优化循环
obj_history = [];
converged = false;

fprintf('开始交替优化...\n');

for iter_AO = 1:max_iter_AO
    fprintf('=== AO迭代 %d ===\n', iter_AO);
    
    %% 子问题1: 波束成形优化 (固定v和U)
    fprintf('优化波束成形...\n');
    [W, obj_BF] = optimize_beamforming(W, v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie, ...
        P_max, sigma2_n, epsilon_cov, max_iter_BCD, tolerance);
    
    %% 子问题2: RIS相位优化 (固定W和U)  
    fprintf('优化RIS相位...\n');
    [v, obj_RIS] = optimize_RIS_phase(W, v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie, ...
        sigma2_n, epsilon_cov, max_iter_BCD, tolerance);
    
    %% 子问题3: FAS位置优化 (固定W和v)
    fprintf('优化FAS位置...\n');
    [U, obj_FAS] = optimize_FAS_position_GWO(W, v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie, ...
        U_region, d_min, sigma2_n, epsilon_cov, GWO_pop_size, GWO_max_iter);
    
    %% 计算当前目标函数值
    current_obj = compute_objective(W, v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie, sigma2_n);
    obj_history(end+1) = current_obj;
    
    fprintf('当前目标值: %.6f\n', current_obj);
    
    %% 收敛性检查
    if iter_AO > 1 && abs(obj_history(end) - obj_history(end-1)) < tolerance
        converged = true;
        fprintf('算法收敛!\n');
        break;
    end
end

%% 结果展示
fprintf('\n=== 优化结果 ===\n');
fprintf('最终目标值: %.6f bps/Hz\n', obj_history(end));
fprintf('总迭代次数: %d\n', length(obj_history));

% 绘制收敛曲线
figure;
plot(1:length(obj_history), obj_history, 'bo-', 'LineWidth', 2, 'MarkerSize', 8);
grid on;
xlabel('迭代次数');
ylabel('最低保密速率 (bps/Hz)');
title('交替优化收敛曲线');

% 绘制最终天线布局
figure;
scatter(U(:,1), U(:,2), 100, 'filled', 'r'); hold on;
scatter(pos_RIS(1), pos_RIS(2), 150, 's', 'filled', 'k');
scatter(pos_Bob(:,1), pos_Bob(:,2), 100, '^', 'filled', 'b');
scatter(pos_Eve(:,1), pos_Eve(:,2), 100, 'v', 'filled', 'm');
scatter(pos_Willie(:,1), pos_Willie(:,2), 100, 'd', 'filled', 'g');
xlabel('x (m)'); ylabel('y (m)');
legend('Alice天线', 'RIS', 'Bob', 'Eve', 'Willie');
title('最终系统布局');
grid on; axis equal;
%% 主函数执行完毕后的后处理
fprintf('\n=== 算法执行完成 ===\n');

% 性能分析
analyze_performance(obj_history, W, v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie, sigma2_n, epsilon_cov);

% 结果可视化
visualize_results(obj_history, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie, v);

% 保存结果
save('RIS_FAS_optimization_results.mat', 'W', 'v', 'U', 'obj_history', ...
     'pos_RIS', 'pos_Bob', 'pos_Eve', 'pos_Willie');

fprintf('\n优化结果已保存到 RIS_FAS_optimization_results.mat\n');
fprintf('图像已保存到 RIS_FAS_optimization_results.png\n');
%% ===== 函数定义 =====

%% 1. 波束成形优化函数
function [W_opt, obj_val] = optimize_beamforming(W_init, v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie, ...
    P_max, sigma2_n, epsilon_cov, max_iter, tolerance)

    [M, K] = size(W_init);
    J = size(pos_Eve, 1);
    L = size(pos_Willie, 1);
    
    % 计算信道矩阵
    [G_Bob, G_Eve, G_Willie] = compute_channels(v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie);
    
    W = W_init;
    obj_history = [];
    
    for iter = 1:max_iter
        % 计算辅助变量
        [alpha, beta] = compute_auxiliary_variables(W, G_Bob, G_Eve, sigma2_n);
        
        % 转换为矩阵形式求解SDP
        [X_opt, obj_val] = solve_beamforming_SDP(alpha, beta, G_Bob, G_Eve, G_Willie, ...
            P_max, sigma2_n, epsilon_cov, M, K, J, L);
        
        % 从矩阵恢复波束成形向量
        W = recover_beamforming_from_matrix(X_opt, M, K);
        
        obj_history(end+1) = obj_val;
        
        % 收敛检查
        if iter > 1 && abs(obj_history(end) - obj_history(end-1)) < tolerance
            break;
        end
    end
    
    W_opt = W;
end

%% 2. 求解波束成形SDP子问题
function [X_opt, obj_val] = solve_beamforming_SDP(alpha, beta, G_Bob, G_Eve, G_Willie, ...
    P_max, sigma2_n, epsilon_cov, M, K, J, L)

    % 使用CVX求解SDP松弛问题
    cvx_begin sdp quiet
        variable X(M*K, M*K) hermitian semidefinite
        variable t_min nonnegative
        variable mu(K, J) nonnegative
        
        maximize t_min
        
        subject to
            % 功率约束 tr(sum_k X_kk) <= P_max
            trace(X) <= P_max;
            
            % 保密速率约束 (转化为SDP形式)
            for k = 1:K
                X_k = extract_user_matrix(X, k, M, K);
                
                % Bob k的SINR约束
                A_Bob_k = G_Bob{k}' * G_Bob{k};
                I_Bob_k = compute_interference_matrix(G_Bob, k, M, K);
                
                % 使用引理1的对数不等式
                alpha_k = alpha(k);
                trace(A_Bob_k * X_k) >= alpha_k * (trace(I_Bob_k * X) + sigma2_n);
                
                % Eve的窃听约束
                for j = 1:J
                    A_Eve_jk = G_Eve{j}' * G_Eve{j};  
                    I_Eve_jk = compute_interference_matrix_eve(G_Eve, j, k, M, K);
                    beta_jk = beta(j, k);
                    
                    trace(A_Eve_jk * X_k) <= beta_jk * (trace(I_Eve_jk * X) + sigma2_n) + mu(k,j);
                    mu(k,j) >= 0;
                end
                
                % 保密速率下界约束
                sum(mu(k,:)) <= (alpha_k - t_min) * (trace(I_Bob_k * X) + sigma2_n);
            end
            
            % 隐蔽约束
            for l = 1:L
                A_Willie_l = compute_willie_matrix(G_Willie{l}, M, K);
                trace(A_Willie_l * X) <= epsilon_cov;
            end
            
    cvx_end
    
    if strcmp(cvx_status, 'Solved') || strcmp(cvx_status, 'Inaccurate/Solved')
        X_opt = X;
        obj_val = cvx_optval;
    else
        warning('SDP求解失败');
        X_opt = eye(M*K) * P_max / (M*K);
        obj_val = -inf;
    end
end

%% 3. RIS相位优化函数  
function [v_opt, obj_val] = optimize_RIS_phase(W, v_init, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie, ...
    sigma2_n, epsilon_cov, max_iter, tolerance)

    N = length(v_init);
    K = size(W, 2);
    J = size(pos_Eve, 1);
    L = size(pos_Willie, 1);
    
    v = v_init;
    obj_history = [];
    
    for iter = 1:max_iter
        % 计算当前信道
        [G_Bob, G_Eve, G_Willie] = compute_channels(v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie);
        
        % 计算辅助变量
        [alpha, beta] = compute_auxiliary_variables(W, G_Bob, G_Eve, sigma2_n);
        
        % 求解RIS相位SDP问题
        [V_opt, obj_val] = solve_RIS_phase_SDP(W, alpha, beta, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie, ...
            sigma2_n, epsilon_cov, N, K, J, L);
        
        % 恢复相位向量
        v = recover_phase_from_matrix(V_opt);
        
        obj_history(end+1) = obj_val;
        
        % 收敛检查
        if iter > 1 && abs(obj_history(end) - obj_history(end-1)) < tolerance
            break;
        end
    end
    
    v_opt = v;
end

%% 4. 求解RIS相位SDP子问题
function [V_opt, obj_val] = solve_RIS_phase_SDP(W, alpha, beta, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie, ...
    sigma2_n, epsilon_cov, N, K, J, L)

    % 计算Alice-RIS和RIS-用户信道
    G_AR = compute_alice_ris_channel(U, pos_RIS);
    [H_RB, H_RE, H_RW] = compute_ris_user_channels(pos_RIS, pos_Bob, pos_Eve, pos_Willie);
    
    cvx_begin sdp quiet
        variable V(N, N) hermitian semidefinite
        variable t_min nonnegative
        variable mu(K, J) nonnegative
        
        maximize t_min
        
        subject to
            % 单位模约束 diag(V) = 1
            for n = 1:N
                V(n, n) == 1;
            end
            
            % 保密速率约束
            for k = 1:K
                % 构造Bob k的等效信道矩阵
                A_Bob_k = construct_channel_matrix(W(:,k), G_AR, H_RB(k,:), 'quad');
                I_Bob_k = construct_interference_matrix(W, k, G_AR, H_RB, 'sum');
                
                % SINR约束 (基于辅助变量)
                trace(A_Bob_k * V) >= alpha(k) * (trace(I_Bob_k * V) + sigma2_n);
                
                % Eve窃听约束
                for j = 1:J
                    A_Eve_jk = construct_channel_matrix(W(:,k), G_AR, H_RE(j,:), 'quad');
                    I_Eve_jk = construct_interference_matrix(W, k, G_AR, H_RE(j,:), 'single');
                    
                    trace(A_Eve_jk * V) <= beta(j,k) * (trace(I_Eve_jk * V) + sigma2_n) + mu(k,j);
                end
                
                % 保密速率约束
                sum(mu(k,:)) <= (alpha(k) - t_min) * (trace(I_Bob_k * V) + sigma2_n);
            end
            
            % 隐蔽约束
            for l = 1:L
                A_Willie_l = construct_interference_matrix(W, 0, G_AR, H_RW(l,:), 'total');
                trace(A_Willie_l * V) <= epsilon_cov;
            end
            
    cvx_end
    
    if strcmp(cvx_status, 'Solved') || strcmp(cvx_status, 'Inaccurate/Solved')
        V_opt = V;
        obj_val = cvx_optval;
    else
        warning('RIS相位SDP求解失败');
        V_opt = eye(N);
        obj_val = -inf;
    end
end

%% 5. FAS位置优化 - 灰狼算法
function [U_opt, obj_val] = optimize_FAS_position_GWO(W, v, U_init, pos_RIS, pos_Bob, pos_Eve, pos_Willie, ...
    U_region, d_min, sigma2_n, epsilon_cov, pop_size, max_iter)

    M = size(U_init, 1);
    dim = 2 * M;  % 2D坐标
    
    % 初始化种群
    pop = initialize_GWO_population(pop_size, M, U_region, d_min);
    
    % 评估初始适应度
    fitness = zeros(pop_size, 1);
    for i = 1:pop_size
        U_i = reshape(pop(i,:), M, 2);
        fitness(i) = evaluate_fitness_FAS(W, v, U_i, pos_RIS, pos_Bob, pos_Eve, pos_Willie, ...
            sigma2_n, epsilon_cov, U_region, d_min);
    end
    
    % 初始化领导者
    [~, sorted_idx] = sort(fitness, 'descend');
    alpha_pos = pop(sorted_idx(1), :);
    beta_pos = pop(sorted_idx(2), :);  
    delta_pos = pop(sorted_idx(3), :);
    alpha_score = fitness(sorted_idx(1));
    
    % GWO主循环
    for iter = 1:max_iter
        a = 2 - iter * (2 / max_iter);  % 线性递减
        
        for i = 1:pop_size
            for j = 1:dim
                % Alpha狼引导
                r1 = rand(); r2 = rand();
                A1 = 2 * a * r1 - a;
                C1 = 2 * r2;
                D_alpha = abs(C1 * alpha_pos(j) - pop(i,j));
                X1 = alpha_pos(j) - A1 * D_alpha;
                
                % Beta狼引导
                r1 = rand(); r2 = rand();
                A2 = 2 * a * r1 - a;
                C2 = 2 * r2;
                D_beta = abs(C2 * beta_pos(j) - pop(i,j));
                X2 = beta_pos(j) - A2 * D_beta;
                
                % Delta狼引导  
                r1 = rand(); r2 = rand();
                A3 = 2 * a * r1 - a;
                C3 = 2 * r2;
                D_delta = abs(C3 * delta_pos(j) - pop(i,j));
                X3 = delta_pos(j) - A3 * D_delta;
                
                % 更新位置
                pop(i,j) = (X1 + X2 + X3) / 3;
            end
            
            % 边界约束
            pop(i,:) = enforce_boundary_constraints(pop(i,:), M, U_region);
        end
        
        % 重新评估适应度
        for i = 1:pop_size
            U_i = reshape(pop(i,:), M, 2);
            fitness(i) = evaluate_fitness_FAS(W, v, U_i, pos_RIS, pos_Bob, pos_Eve, pos_Willie, ...
                sigma2_n, epsilon_cov, U_region, d_min);
        end
        
        % 更新领导者
        [~, sorted_idx] = sort(fitness, 'descend');
        if fitness(sorted_idx(1)) > alpha_score
            alpha_score = fitness(sorted_idx(1));
            alpha_pos = pop(sorted_idx(1), :);
        end
        beta_pos = pop(sorted_idx(2), :);
        delta_pos = pop(sorted_idx(3), :);
    end
    
    U_opt = reshape(alpha_pos, M, 2);
    obj_val = alpha_score;
end

%% 6. 适应度函数评估 (FAS位置优化)
function fitness = evaluate_fitness_FAS(W, v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie, ...
    sigma2_n, epsilon_cov, U_region, d_min)

    % 基础目标函数
    obj_val = compute_objective(W, v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie, sigma2_n);
    
    % 惩罚项
    penalty = 0;
    penalty_weight = 1e6;
    
    % 区域约束惩罚
    for m = 1:size(U, 1)
        if U(m,1) < U_region(1,1) || U(m,1) > U_region(1,2) || ...
           U(m,2) < U_region(2,1) || U(m,2) > U_region(2,2)
            penalty = penalty + penalty_weight;
        end
    end
    
    % 最小间距约束惩罚
    M = size(U, 1);
    for i = 1:M
        for j = i+1:M
            dist = norm(U(i,:) - U(j,:));
            if dist < d_min
                penalty = penalty + penalty_weight * (d_min - dist);
            end
        end
    end
    
    % 隐蔽约束惩罚
    [~, ~, G_Willie] = compute_channels(v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie);
    total_power_willie = 0;
    for l = 1:length(G_Willie)
        for k = 1:size(W, 2)
            total_power_willie = total_power_willie + abs(G_Willie{l}' * W(:,k))^2;
        end
    end
    if total_power_willie > epsilon_cov
        penalty = penalty + penalty_weight * (total_power_willie - epsilon_cov);
    end
    
    fitness = obj_val - penalty;
end

%% 7. 计算目标函数值
function obj_val = compute_objective(W, v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie, sigma2_n)
    
    % 计算所有信道
    [G_Bob, G_Eve, ~] = compute_channels(v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie);
    
    K = size(W, 2);
    J = length(G_Eve);
    
    min_secrecy_rate = inf;
    
    for k = 1:K
        % 计算Bob k的SINR
        signal_power = abs(G_Bob{k}' * W(:,k))^2;
        interference = 0;
        for i = 1:K
            if i ~= k
                interference = interference + abs(G_Bob{k}' * W(:,i))^2;
            end
        end
        SINR_Bob_k = signal_power / (interference + sigma2_n);
        R_Bob_k = log2(1 + SINR_Bob_k);
        
        % 计算所有Eve对Bob k的最大窃听速率
        max_eve_rate = 0;
        for j = 1:J
            signal_power_eve = abs(G_Eve{j}' * W(:,k))^2;
            interference_eve = 0;
            for i = 1:K
                if i ~= k
                    interference_eve = interference_eve + abs(G_Eve{j}' * W(:,i))^2;
                end
            end
            SINR_Eve_jk = signal_power_eve / (interference_eve + sigma2_n);
            R_Eve_jk = log2(1 + SINR_Eve_jk);
            max_eve_rate = max(max_eve_rate, R_Eve_jk);
        end
        
        % 计算保密速率
        secrecy_rate_k = max(0, R_Bob_k - max_eve_rate);
        min_secrecy_rate = min(min_secrecy_rate, secrecy_rate_k);
    end
    
    obj_val = min_secrecy_rate;
end

%% 8. 信道计算函数
function [G_Bob, G_Eve, G_Willie] = compute_channels(v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie)
    
    % Alice-RIS信道
    G_AR = compute_alice_ris_channel(U, pos_RIS);
    
    % RIS-用户信道
    [H_RB, H_RE, H_RW] = compute_ris_user_channels(pos_RIS, pos_Bob, pos_Eve, pos_Willie);
    
    % 等效端到端信道
    Theta = diag(v);
    
    % Bob信道
    K = size(pos_Bob, 1);
    G_Bob = cell(K, 1);
    for k = 1:K
        G_Bob{k} = G_AR * Theta * H_RB(k,:)';
    end
    
    % Eve信道
    J = size(pos_Eve, 1);
    G_Eve = cell(J, 1);
    for j = 1:J
        G_Eve{j} = G_AR * Theta * H_RE(j,:)';
    end
    
    % Willie信道
    L = size(pos_Willie, 1);
    G_Willie = cell(L, 1);
    for l = 1:L
        G_Willie{l} = G_AR * Theta * H_RW(l,:)';
    end
end

%% 9. Alice-RIS信道计算
function G_AR = compute_alice_ris_channel(U, pos_RIS)
    
    M = size(U, 1);
    N = 16;  % RIS单元数量
    fc = 2.4e9;
    c = 3e8;
    lambda = c/fc;
    
    G_AR = zeros(N, M);
    
    for m = 1:M
        for n = 1:N
            % 计算距离
            d = norm(U(m,:) - pos_RIS);
            
            % 路径损耗
            path_loss = db2pow(-30) * d^(-2.2);  % Alice-RIS路径损耗
            
            % 相位项 (假设视距传播)
            phase = -2*pi*d/lambda;
            
            G_AR(n, m) = sqrt(path_loss) * exp(1j*phase);
        end
    end
end

%% 10. RIS-用户信道计算
function [H_RB, H_RE, H_RW] = compute_ris_user_channels(pos_RIS, pos_Bob, pos_Eve, pos_Willie)
    
    N = 16;  % RIS单元数量
    fc = 2.4e9;
    c = 3e8;
    lambda = c/fc;
    
    % RIS-Bob信道
    K = size(pos_Bob, 1);
    H_RB = zeros(K, N);
    for k = 1:K
        for n = 1:N
            d = norm(pos_Bob(k,:) - pos_RIS);
            path_loss = db2pow(-30) * d^(-2.8);
            phase = -2*pi*d/lambda;
            H_RB(k, n) = sqrt(path_loss) * exp(1j*phase) * (randn + 1j*randn)/sqrt(2);
        end
    end
    
    % RIS-Eve信道
    J = size(pos_Eve, 1);
    H_RE = zeros(J, N);
    for j = 1:J
        for n = 1:N
            d = norm(pos_Eve(j,:) - pos_RIS);
            path_loss = db2pow(-30) * d^(-2.8);
            phase = -2*pi*d/lambda;
            H_RE(j, n) = sqrt(path_loss) * exp(1j*phase) * (randn + 1j*randn)/sqrt(2);
        end
    end
    
    % RIS-Willie信道
    L = size(pos_Willie, 1);
    H_RW = zeros(L, N);
    for l = 1:L
        for n = 1:N
            d = norm(pos_Willie(l,:) - pos_RIS);
            path_loss = db2pow(-30) * d^(-2.8);
            phase = -2*pi*d/lambda;
            H_RW(l, n) = sqrt(path_loss) * exp(1j*phase) * (randn + 1j*randn)/sqrt(2);
        end
    end
end

%% 11. 辅助函数 - 计算辅助变量
function [alpha, beta] = compute_auxiliary_variables(W, G_Bob, G_Eve, sigma2_n)
    
    K = size(W, 2);
    J = length(G_Eve);
    
    alpha = zeros(K, 1);
    beta = zeros(J, K);
    
    for k = 1:K
        % 计算Bob k的SINR
        signal_power = abs(G_Bob{k}' * W(:,k))^2;
        interference = sigma2_n;
        for i = 1:K
            if i ~= k
                interference = interference + abs(G_Bob{k}' * W(:,i))^2;
            end
        end
        SINR_k = signal_power / interference;
        alpha(k) = SINR_k / (1 + SINR_k);
        
        % 计算Eve对Bob k的SINR
        for j = 1:J
            signal_power_eve = abs(G_Eve{j}' * W(:,k))^2;
            interference_eve = sigma2_n;
            for i = 1:K
                if i ~= k
                    interference_eve = interference_eve + abs(G_Eve{j}' * W(:,i))^2;
                end
            end
            SINR_jk = signal_power_eve / interference_eve;
            beta(j, k) = SINR_jk / (1 + SINR_jk);
        end
    end
end

%% 12. 初始化天线位置函数
function U = generate_initial_antenna_positions(M, U_region, d_min)
    U = zeros(M, 2);
    attempts = 0;
    max_attempts = 1000;
    
    for m = 1:M
        valid_position = false;
        while ~valid_position && attempts < max_attempts
            attempts = attempts + 1;
            % 随机生成位置
            x = U_region(1,1) + rand() * (U_region(1,2) - U_region(1,1));
            y = U_region(2,1) + rand() * (U_region(2,2) - U_region(2,1));
            candidate = [x, y];
            
            % 检查与已有天线的距离
            valid_position = true;
            for i = 1:m-1
                if norm(candidate - U(i,:)) < d_min
                    valid_position = false;
                    break;
                end
            end
            
            if valid_position
                U(m,:) = candidate;
            end
        end
        
        if attempts >= max_attempts
            warning('无法满足最小间距约束，使用网格布局');
            U = generate_grid_layout(M, U_region, d_min);
            return;
        end
    end
end

%% 13. 网格布局生成函数
function U = generate_grid_layout(M, U_region, d_min)
    grid_size = ceil(sqrt(M));
    x_step = (U_region(1,2) - U_region(1,1)) / (grid_size + 1);
    y_step = (U_region(2,2) - U_region(2,1)) / (grid_size + 1);
    
    U = zeros(M, 2);
    idx = 1;
    for i = 1:grid_size
        for j = 1:grid_size
            if idx <= M
                U(idx, 1) = U_region(1,1) + i * x_step;
                U(idx, 2) = U_region(2,1) + j * y_step;
                idx = idx + 1;
            end
        end
    end
end

%% 14. 从矩阵恢复波束成形向量
function W = recover_beamforming_from_matrix(X, M, K)
    W = zeros(M, K);
    
    for k = 1:K
        % 提取用户k对应的子矩阵
        start_idx = (k-1)*M + 1;
        end_idx = k*M;
        X_k = X(start_idx:end_idx, start_idx:end_idx);
        
        % 特征值分解
        [V, D] = eig(X_k);
        [~, max_idx] = max(diag(real(D)));
        
        % 主特征向量作为波束成形向量
        W(:, k) = sqrt(max(0, real(D(max_idx, max_idx)))) * V(:, max_idx);
    end
end

%% 15. 提取用户矩阵函数
function X_k = extract_user_matrix(X, k, M, K)
    start_idx = (k-1)*M + 1;
    end_idx = k*M;
    X_k = X(start_idx:end_idx, start_idx:end_idx);
end

%% 16. 计算干扰矩阵
function I_matrix = compute_interference_matrix(G_Bob, k, M, K)
    I_matrix = zeros(M*K, M*K);
    
    for i = 1:K
        if i ~= k
            start_idx = (i-1)*M + 1;
            end_idx = i*M;
            G_i = G_Bob{k};  % Bob k受到用户i的干扰
            I_matrix(start_idx:end_idx, start_idx:end_idx) = G_i * G_i';
        end
    end
end

%% 17. 计算Eve干扰矩阵
function I_matrix = compute_interference_matrix_eve(G_Eve, j, k, M, K)
    I_matrix = zeros(M*K, M*K);
    
    for i = 1:K
        if i ~= k
            start_idx = (i-1)*M + 1;
            end_idx = i*M;
            G_ji = G_Eve{j};  % Eve j受到用户i的干扰
            I_matrix(start_idx:end_idx, start_idx:end_idx) = G_ji * G_ji';
        end
    end
end

%% 18. 计算Willie矩阵
function A_matrix = compute_willie_matrix(G_Willie_l, M, K)
    A_matrix = zeros(M*K, M*K);
    
    for k = 1:K
        start_idx = (k-1)*M + 1;
        end_idx = k*M;
        A_matrix(start_idx:end_idx, start_idx:end_idx) = G_Willie_l * G_Willie_l';
    end
end

%% 19. 从矩阵恢复相位向量
function v = recover_phase_from_matrix(V)
    N = size(V, 1);
    
    % 特征值分解
    [U, D] = eig(V);
    [~, max_idx] = max(diag(real(D)));
    
    % 主特征向量
    v_temp = U(:, max_idx);
    
    % 归一化为单位模
    v = exp(1j * angle(v_temp));
end

%% 20. 构造信道矩阵
function A_matrix = construct_channel_matrix(w_k, G_AR, h_user, type)
    N = size(G_AR, 1);
    
    switch type
        case 'quad'
            % 二次型 |h^H Θ G w_k|^2 = w_k^H G^H Θ^H h h^H Θ G w_k
            h_vec = h_user(:);
            temp = G_AR' * diag(h_vec);
            A_matrix = temp' * temp;
            
        case 'linear'
            % 线性型
            h_vec = h_user(:);
            A_matrix = diag(h_vec) * G_AR;
    end
end

%% 21. 构造干扰矩阵
function I_matrix = construct_interference_matrix(W, k, G_AR, H_users, type)
    [num_users, N] = size(H_users);
    K = size(W, 2);
    
    I_matrix = zeros(N, N);
    
    switch type
        case 'sum'
            % 所有其他用户的干扰
            for i = 1:K
                if i ~= k
                    h_vec = H_users(1, :);  % 假设所有用户看到相同的RIS
                    temp = G_AR' * diag(h_vec);
                    I_matrix = I_matrix + temp' * temp;
                end
            end
            
        case 'single'
            % 单个用户的干扰
            for i = 1:K
                if i ~= k
                    user_idx = min(size(H_users, 1), 1);
                    h_vec = H_users(user_idx, :);
                    temp = G_AR' * diag(h_vec);
                    I_matrix = I_matrix + temp' * temp;
                end
            end
            
        case 'total'
            % 所有用户的总功率
            for i = 1:K
                user_idx = min(size(H_users, 1), i);
                h_vec = H_users(user_idx, :);
                temp = G_AR' * diag(h_vec);
                I_matrix = I_matrix + temp' * temp;
            end
    end
end

%% 22. 初始化GWO种群
function pop = initialize_GWO_population(pop_size, M, U_region, d_min)
    dim = 2 * M;
    pop = zeros(pop_size, dim);
    
    for i = 1:pop_size
        valid_individual = false;
        attempts = 0;
        max_attempts = 100;
        
        while ~valid_individual && attempts < max_attempts
            attempts = attempts + 1;
            individual = zeros(1, dim);
            
            % 随机生成M个天线位置
            for m = 1:M
                individual(2*m-1) = U_region(1,1) + rand() * (U_region(1,2) - U_region(1,1));
                individual(2*m) = U_region(2,1) + rand() * (U_region(2,2) - U_region(2,1));
            end
            
            % 检查约束
            if check_distance_constraints(individual, M, d_min)
                pop(i, :) = individual;
                valid_individual = true;
            end
        end
        
        if ~valid_individual
            % 使用网格布局作为备选
            U_grid = generate_grid_layout(M, U_region, d_min);
            pop(i, :) = reshape(U_grid', 1, dim);
        end
    end
end

%% 23. 检查距离约束
function valid = check_distance_constraints(individual, M, d_min)
    valid = true;
    U = reshape(individual, 2, M)';
    
    for i = 1:M
        for j = i+1:M
            if norm(U(i,:) - U(j,:)) < d_min
                valid = false;
                return;
            end
        end
    end
end

%% 24. 强制边界约束
function individual = enforce_boundary_constraints(individual, M, U_region)
    for m = 1:M
        % x坐标约束
        individual(2*m-1) = max(U_region(1,1), min(U_region(1,2), individual(2*m-1)));
        % y坐标约束
        individual(2*m) = max(U_region(2,1), min(U_region(2,2), individual(2*m)));
    end
end

%% 25. 性能分析函数
function analyze_performance(obj_history, W, v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie, sigma2_n, epsilon_cov)
    
    fprintf('\n=== 性能分析 ===\n');
    
    % 计算最终性能指标
    [G_Bob, G_Eve, G_Willie] = compute_channels(v, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie);
    
    K = size(W, 2);
    J = length(G_Eve);
    L = length(G_Willie);
    
    % 保密速率分析
    fprintf('各用户保密速率:\n');
    total_secrecy_rate = 0;
    for k = 1:K
        % Bob SINR
        signal_power = abs(G_Bob{k}' * W(:,k))^2;
        interference = sigma2_n;
        for i = 1:K
            if i ~= k
                interference = interference + abs(G_Bob{k}' * W(:,i))^2;
            end
        end
        SINR_Bob = signal_power / interference;
        R_Bob = log2(1 + SINR_Bob);
        
        % 最大窃听速率
        max_eve_rate = 0;
        for j = 1:J
            signal_power_eve = abs(G_Eve{j}' * W(:,k))^2;
            interference_eve = sigma2_n;
            for i = 1:K
                if i ~= k
                    interference_eve = interference_eve + abs(G_Eve{j}' * W(:,i))^2;
                end
            end
            SINR_Eve = signal_power_eve / interference_eve;
            R_Eve = log2(1 + SINR_Eve);
            max_eve_rate = max(max_eve_rate, R_Eve);
        end
        
        secrecy_rate = max(0, R_Bob - max_eve_rate);
        total_secrecy_rate = total_secrecy_rate + secrecy_rate;
        
        fprintf('  用户 %d: %.4f bps/Hz (Bob: %.4f, Max Eve: %.4f)\n', ...
            k, secrecy_rate, R_Bob, max_eve_rate);
    end
    
    fprintf('总保密速率: %.4f bps/Hz\n', total_secrecy_rate);
    
    % 隐蔽性分析
    fprintf('\n隐蔽性分析:\n');
    for l = 1:L
        total_power = 0;
        for k = 1:K
            total_power = total_power + abs(G_Willie{l}' * W(:,k))^2;
        end
        fprintf('  Willie %d 接收功率: %.2e W (阈值: %.2e W)\n', ...
            l, total_power, epsilon_cov);
    end
    
    % 功率消耗
    total_power = 0;
    for k = 1:K
        total_power = total_power + norm(W(:,k))^2;
    end
    fprintf('\n总功率消耗: %.4f W (最大: %.4f W)\n', total_power, 1);
    
    % 收敛性分析
    fprintf('\n收敛性分析:\n');
    fprintf('  迭代次数: %d\n', length(obj_history));
    if length(obj_history) > 1
        improvement = obj_history(end) - obj_history(1);
        fprintf('  性能提升: %.6f bps/Hz (%.2f%%)\n', ...
            improvement, improvement/abs(obj_history(1))*100);
    end
end

%% 26. 结果可视化函数
function visualize_results(obj_history, U, pos_RIS, pos_Bob, pos_Eve, pos_Willie, v)
    
    % 收敛曲线
    figure('Position', [100, 100, 1200, 400]);
    
    subplot(1, 3, 1);
    plot(1:length(obj_history), obj_history, 'bo-', 'LineWidth', 2, 'MarkerSize', 6);
    grid on;
    xlabel('迭代次数');
    ylabel('最小保密速率 (bps/Hz)');
    title('算法收敛曲线');
    
    % 系统布局
    subplot(1, 3, 2);
    scatter(U(:,1), U(:,2), 100, 'filled', 'r', 's'); hold on;
    scatter(pos_RIS(1), pos_RIS(2), 150, 'filled', 'k', 'd');
    scatter(pos_Bob(:,1), pos_Bob(:,2), 100, 'filled', 'b', '^');
    scatter(pos_Eve(:,1), pos_Eve(:,2), 100, 'filled', 'm', 'v');
    scatter(pos_Willie(:,1), pos_Willie(:,2), 100, 'filled', 'g', 'o');
    
    % 添加连接线
    for i = 1:size(U, 1)
        plot([U(i,1), pos_RIS(1)], [U(i,2), pos_RIS(2)], 'k--', 'Alpha', 0.3);
    end
    for k = 1:size(pos_Bob, 1)
        plot([pos_RIS(1), pos_Bob(k,1)], [pos_RIS(2), pos_Bob(k,2)], 'b--', 'Alpha', 0.5);
    end
    
    xlabel('x (m)'); ylabel('y (m)');
    legend('Alice天线', 'RIS', 'Bob', 'Eve', 'Willie', 'Location', 'best');
    title('系统布局图');
    grid on; axis equal;
    
    % RIS相位分布
    subplot(1, 3, 3);
    phase_deg = angle(v) * 180 / pi;
    stem(1:length(v), phase_deg, 'filled', 'LineWidth', 1.5);
    xlabel('RIS单元索引');
    ylabel('相位 (度)');
    title('RIS相位分布');
    grid on;
    ylim([-180, 180]);
    
    % 保存图像
    saveas(gcf, 'RIS_FAS_optimization_results.png');
end



%% 可选：参数敏感性分析
function sensitivity_analysis()
    fprintf('\n=== 参数敏感性分析 ===\n');
    
    % 不同RIS单元数量的影响
    N_values = [8, 16, 32, 64];
    secrecy_rates_N = zeros(size(N_values));
    
    for i = 1:length(N_values)
        fprintf('测试 N = %d...\n', N_values(i));
        % 这里可以重新运行优化算法，记录结果
        % secrecy_rates_N(i) = run_optimization_with_N(N_values(i));
    end
    
    % 不同功率预算的影响
    P_values = [0.1, 0.5, 1, 2, 5];
    secrecy_rates_P = zeros(size(P_values));
    
    % 绘制敏感性分析结果
    figure;
    subplot(1, 2, 1);
    plot(N_values, secrecy_rates_N, 'ro-', 'LineWidth', 2);
    xlabel('RIS单元数量');
    ylabel('保密速率 (bps/Hz)');
    title('RIS单元数量影响');
    grid on;
    
    subplot(1, 2, 2);
    plot(P_values, secrecy_rates_P, 'bo-', 'LineWidth', 2);
    xlabel('功率预算 (W)');
    ylabel('保密速率 (bps/Hz)');
    title('功率预算影响');
    grid on;
end